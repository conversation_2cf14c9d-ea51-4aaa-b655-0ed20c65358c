# Pandoc FastAPI Service

一个基于 FastAPI 的 Pandoc HTTP API 服务，提供文档转换和模板管理功能。

## 功能特性

- 🔄 **文档转换**: 支持 Pandoc 的所有输入输出格式
- 📁 **模板管理**: 管理 DOCX 模板、CSL 样式、Lua 过滤器等资源，支持元数据和自动加载
- 📦 **多文件处理**: 自动检测和打包多个输出文件
- 🐳 **Docker 部署**: 完整的 Docker 容器化方案
- 🗄️ **数据库管理**: 使用 SQLite 管理模板资源
- 🔧 **灵活配置**: 支持丰富的 Pandoc 参数和元数据

## 快速开始

### 使用 Docker (推荐)

1. 克隆项目：
```bash
git clone <repository-url>
cd pandoc-fastapi
```

2. 使用 Docker Compose 启动：
```bash
cd docker
docker-compose up -d
```

3. 访问 API 文档：
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

### 本地开发

1. 安装依赖：
```bash
# 安装 Pandoc
sudo apt-get install pandoc  # Ubuntu/Debian
# 或
brew install pandoc  # macOS

# 安装 Python 依赖
uv sync
```

2. 配置环境：
```bash
cp .env.example .env
# 编辑 .env 文件配置参数
```

3. 启动服务：
```bash
uv run uvicorn app.main:app --reload
```

## API 使用

### 文档转换

转换 Markdown 到 HTML：
```bash
curl -X POST "http://localhost:8000/api/v1/convert/" \
  -F "file=@document.md" \
  -F "to_format=html"
```

使用模板转换到 DOCX：
```bash
curl -X POST "http://localhost:8000/api/v1/convert/" \
  -F "file=@document.md" \
  -F "to_format=docx" \
  -F "docx_template=my_template"
```

### 模板管理

上传 DOCX 模板：
```bash
curl -X POST "http://localhost:8000/api/v1/templates/" \
  -F "file=@template.docx" \
  -F "name=my_template" \
  -F "display_name=My Template" \
  -F "resource_type=docx_template" \
  -F "metadata={\"version\":\"1.0.0\",\"author\":\"Your Name\",\"tags\":[\"business\"]}"
```

列出所有模板：
```bash
curl "http://localhost:8000/api/v1/templates/"
```

重新加载模板：
```bash
curl -X POST "http://localhost:8000/api/v1/templates/reload"
```

搜索模板：
```bash
curl -X POST "http://localhost:8000/api/v1/templates/search" \
  -H "Content-Type: application/json" \
  -d '{"query":"business","tags":["professional"]}'
```

## 项目结构

```
pandoc-fastapi/
├── app/                    # 应用代码
│   ├── api/               # API 路由
│   ├── core/              # 核心功能
│   ├── models/            # 数据库模型
│   ├── schemas/           # Pydantic 模式
│   └── utils/             # 工具函数
├── templates/             # 模板文件存储
├── storage/               # 文件存储
├── tests/                 # 测试文件
├── docker/                # Docker 配置
└── docs/                  # 文档
```

## 配置说明

主要配置项（.env 文件）：

```env
# 数据库
DATABASE_URL=sqlite:///./pandoc_api.db

# 存储路径
UPLOAD_DIR=storage/uploads
OUTPUT_DIR=storage/outputs
TEMPLATE_DIR=templates

# API 设置
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=false

# Pandoc 设置
PANDOC_TIMEOUT=300
MAX_FILE_SIZE=100MB
```

## 支持的格式

### 输入格式
- Markdown (各种变体)
- HTML
- DOCX
- ODT
- LaTeX
- reStructuredText
- 等等...

### 输出格式
- HTML
- PDF
- DOCX
- ODT
- EPUB
- LaTeX
- 等等...

## 模板系统

### 模板类型

系统支持以下模板类型：
- **DOCX 模板**: Microsoft Word 文档模板 (.docx)
- **CSL 样式**: 引用样式语言文件 (.csl)
- **Lua 过滤器**: Pandoc Lua 过滤器 (.lua)
- **其他资源**: 其他类型的资源文件

### 模板元数据

每个模板都可以包含丰富的元数据：
```json
{
  "name": "template_name",
  "display_name": "Template Display Name",
  "description": "Template description",
  "version": "1.0.0",
  "author": "Author Name",
  "tags": ["tag1", "tag2"],
  "supported_formats": ["docx", "pdf"],
  "custom_fields": {
    "category": "business",
    "complexity": "simple"
  }
}
```

### 内置模板和用户模板

- **内置模板**: 存放在 `templates/builtin/` 目录，应用启动时自动加载
- **用户模板**: 存放在 `templates/user/` 目录，可通过 API 上传或直接放置

### 模板元数据文件

为模板文件创建同名的 `.json` 文件来定义元数据：
```
templates/builtin/
├── business_template.docx
├── business_template.docx.json  # 元数据文件
├── academic_style.csl
└── academic_style.csl.json      # 元数据文件
```

### 模板管理 API

重新加载模板：
```bash
curl -X POST "http://localhost:8000/api/v1/templates/reload"
```

搜索模板：
```bash
curl -X POST "http://localhost:8000/api/v1/templates/search" \
  -H "Content-Type: application/json" \
  -d '{"query":"business","tags":["professional"]}'
```

列出内置模板：
```bash
curl "http://localhost:8000/api/v1/templates/builtin/"
```

## 开发

### 运行测试

```bash
uv run pytest
```

### 代码格式化

```bash
uv run black app/
uv run isort app/
```

## 部署

### Docker 部署

1. 构建镜像：
```bash
docker build -f docker/Dockerfile -t pandoc-fastapi .
```

2. 运行容器：
```bash
docker run -p 8000:8000 -v pandoc_data:/app/data pandoc-fastapi
```

### 生产环境注意事项

1. 修改默认密钥：
```env
SECRET_KEY=your-secure-secret-key
```

2. 配置 CORS：
```python
# 在 app/main.py 中修改
allow_origins=["https://yourdomain.com"]
```

3. 使用反向代理（Nginx）
4. 配置日志记录
5. 设置文件清理策略

## 许可证

MIT License

## 贡献

欢迎提交 Issue 和 Pull Request！