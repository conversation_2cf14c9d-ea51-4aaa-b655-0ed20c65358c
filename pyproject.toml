[project]
name = "pandoc-fastapi"
version = "0.1.0"
description = "FastAPI service for Pandoc document conversion with template management"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "aiofiles>=24.1.0",
    "alembic>=1.16.4",
    "fastapi[standard]>=0.116.1",
    "pydantic-settings>=2.10.1",
    "python-multipart>=0.0.20",
    "sqlalchemy>=2.0.42",
    "uvicorn>=0.35.0",
]

[dependency-groups]
dev = [
    "httpx>=0.28.1",
    "pytest-asyncio>=1.1.0",
    "pytest>=8.4.1",
]
