"""Template and resource models"""

from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Dict, Any, Optional

from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean, JSON
from sqlalchemy.sql import func

from app.core.database import Base


class ResourceType(str, Enum):
    """Resource type enumeration"""
    DOCX_TEMPLATE = "docx_template"
    CSL_STYLE = "csl_style"
    LUA_FILTER = "lua_filter"
    OTHER = "other"


class TemplateResource(Base):
    """Template and resource files model"""

    __tablename__ = "template_resources"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), unique=True, index=True, nullable=False)
    display_name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    resource_type = Column(String(50), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer, nullable=False)
    mime_type = Column(String(100), nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    is_builtin = Column(Boolean, default=False, nullable=False)
    metadata = Column(JSON, nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    def __repr__(self):
        return f"<TemplateResource(name='{self.name}', type='{self.resource_type}')>"
    
    @property
    def file_exists(self) -> bool:
        """Check if the file exists on disk"""
        return Path(self.file_path).exists()
    
    def get_absolute_path(self) -> Path:
        """Get absolute path to the file"""
        return Path(self.file_path).resolve()

    def get_metadata(self) -> Dict[str, Any]:
        """Get template metadata as dictionary"""
        return self.metadata or {}

    def set_metadata(self, metadata: Dict[str, Any]) -> None:
        """Set template metadata"""
        self.metadata = metadata

    def update_metadata(self, updates: Dict[str, Any]) -> None:
        """Update specific metadata fields"""
        current_metadata = self.get_metadata()
        current_metadata.update(updates)
        self.metadata = current_metadata
