"""Template and resource models"""

from datetime import datetime
from enum import Enum
from pathlib import Path

from sqlalchemy import Column, Integer, String, DateTime, Text, Boolean
from sqlalchemy.sql import func

from app.core.database import Base


class ResourceType(str, Enum):
    """Resource type enumeration"""
    DOCX_TEMPLATE = "docx_template"
    CSL_STYLE = "csl_style"
    BIB_FILE = "bib_file"
    LUA_FILTER = "lua_filter"
    OTHER = "other"


class TemplateResource(Base):
    """Template and resource files model"""
    
    __tablename__ = "template_resources"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), unique=True, index=True, nullable=False)
    display_name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    resource_type = Column(String(50), nullable=False)
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer, nullable=False)
    mime_type = Column(String(100), nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    def __repr__(self):
        return f"<TemplateResource(name='{self.name}', type='{self.resource_type}')>"
    
    @property
    def file_exists(self) -> bool:
        """Check if the file exists on disk"""
        return Path(self.file_path).exists()
    
    def get_absolute_path(self) -> Path:
        """Get absolute path to the file"""
        return Path(self.file_path).resolve()
