"""Main FastAPI application"""

import logging
from contextlib import asynccontextmanager

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from app.api import pandoc, templates
from app.core.config import settings
from app.core.database import create_tables, get_db
from app.core.template_manager import TemplateManager

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan events"""
    # Startup
    create_tables()

    # Load templates on startup
    try:
        db = next(get_db())
        template_manager = TemplateManager(db)
        results = await template_manager.scan_and_load_templates()
        logger.info(f"Template loading completed: {results}")
    except Exception as e:
        logger.error(f"Failed to load templates on startup: {e}")

    yield
    # Shutdown
    pass


# Create FastAPI application
app = FastAPI(
    title="Pandoc FastAPI Service",
    description="HTTP API service for Pandoc document conversion with template management",
    version="0.1.0",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(pandoc.router, prefix="/api/v1")
app.include_router(templates.router, prefix="/api/v1")


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "Pandoc FastAPI Service",
        "version": "0.1.0",
        "docs": "/docs",
        "redoc": "/redoc"
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy"}


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.debug
    )
