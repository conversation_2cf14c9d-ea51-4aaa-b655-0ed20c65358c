#!/usr/bin/env python3
"""
Simple example of using the Pandoc FastAPI service
"""

import requests
import json
from pathlib import Path


def main():
    """Demonstrate basic usage of the Pandoc API"""
    
    base_url = "http://localhost:8000"
    
    # Check if service is running
    try:
        response = requests.get(f"{base_url}/health")
        if response.status_code != 200:
            print("❌ Service is not running. Please start the service first.")
            return
        print("✅ Service is running")
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to service. Please start the service first.")
        print("Run: uv run python run.py")
        return
    
    # Create a sample markdown file
    sample_md = """# Sample Document

This is a **sample document** with some *formatting*.

## Features

- Bullet points
- **Bold text**
- *Italic text*

## Code Example

```python
def hello_world():
    print("Hello, World!")
```

## Citation

This is a citation [@smith2023].

## References
"""
    
    # Save sample file
    sample_file = Path("sample.md")
    sample_file.write_text(sample_md)
    
    try:
        # Example 1: Convert Markdown to HTML
        print("\n📄 Converting Markdown to HTML...")
        
        with open(sample_file, 'rb') as f:
            files = {'file': ('sample.md', f, 'text/markdown')}
            data = {'to_format': 'html'}
            
            response = requests.post(f"{base_url}/api/v1/convert/", files=files, data=data)
            
            if response.status_code == 200:
                result = response.json()
                if result['success']:
                    print(f"✅ Conversion successful!")
                    print(f"   Output files: {result['output_files']}")
                    print(f"   Download URL: {result['download_url']}")
                    print(f"   Conversion time: {result['conversion_time']:.2f}s")
                else:
                    print(f"❌ Conversion failed: {result['message']}")
            else:
                print(f"❌ Request failed: {response.status_code}")
                print(response.text)
        
        # Example 2: Get supported formats
        print("\n📋 Getting supported formats...")
        response = requests.get(f"{base_url}/api/v1/convert/formats")
        if response.status_code == 200:
            formats = response.json()
            print(f"✅ Input formats: {len(formats['input_formats'])} available")
            print(f"✅ Output formats: {len(formats['output_formats'])} available")
            print(f"   Some input formats: {formats['input_formats'][:5]}")
            print(f"   Some output formats: {formats['output_formats'][:5]}")
        
        # Example 3: List templates
        print("\n📁 Listing templates...")
        response = requests.get(f"{base_url}/api/v1/templates/")
        if response.status_code == 200:
            templates = response.json()
            print(f"✅ Found {templates['total']} templates")
            for template in templates['resources']:
                print(f"   - {template['name']}: {template['display_name']} ({template['resource_type']})")
        
        # Example 4: Get resource types
        print("\n🏷️  Getting resource types...")
        response = requests.get(f"{base_url}/api/v1/templates/types/")
        if response.status_code == 200:
            types = response.json()
            print("✅ Available resource types:")
            for rt in types['resource_types']:
                print(f"   - {rt['name']}: {rt['description']}")
        
    finally:
        # Cleanup
        if sample_file.exists():
            sample_file.unlink()
    
    print("\n🎉 Demo completed!")
    print("\nNext steps:")
    print("1. Upload some templates using the /api/v1/templates/ endpoint")
    print("2. Try converting with templates using template names")
    print("3. Explore the API documentation at http://localhost:8000/docs")


if __name__ == "__main__":
    main()
